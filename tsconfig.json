{
  "compilerOptions": {
    "baseUrl": "./",
    "rootDir": "src",
    "paths": {
      "~/*": ["./src/*"]
    },

    "target": "ES2022",
    "lib": ["ES2022", "DOM"],
    "module": "NodeNext",
    "moduleResolution": "NodeNext",
    "resolveJsonModule": true,
    "verbatimModuleSyntax": true,
    "allowJs": true,
    "checkJs": true,

    /* EMIT RULES */
    "outDir": "./dist",
    "declaration": true,
    "declarationMap": true,
    "sourceMap": true,
    "removeComments": true,

    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true
  },
  "include": ["src/**/*"]
}
