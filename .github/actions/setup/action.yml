name: "Setup and install"
description: "Common setup steps for Actions"

runs:
  using: composite
  steps:
    - name: Install pnpm
      uses: pnpm/action-setup@v4
      with:
        version: 10.10.0
    - name: Install Node.js v20
      uses: actions/setup-node@v4
      with:
        node-version: 20.17.0
        cache: "pnpm"

    - name: Install PNPM Dependencies
      shell: bash
      run: pnpm install
