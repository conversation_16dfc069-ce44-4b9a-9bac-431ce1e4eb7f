{"parser": "@typescript-eslint/parser", "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "prettier"], "plugins": ["@typescript-eslint"], "parserOptions": {"ecmaVersion": 2022, "sourceType": "module"}, "rules": {"@typescript-eslint/explicit-function-return-type": "warn", "@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_"}], "@typescript-eslint/no-explicit-any": "warn"}}