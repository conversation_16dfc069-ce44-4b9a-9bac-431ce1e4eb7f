{"name": "figma-developer-mcp", "version": "0.4.3", "description": "Model Context Protocol server for Figma integration", "type": "module", "main": "dist/index.js", "bin": {"figma-developer-mcp": "dist/cli.js"}, "files": ["dist", "README.md"], "scripts": {"build": "tsup --dts", "type-check": "tsc --noEmit", "test": "jest", "start": "node dist/cli.js", "start:cli": "cross-env NODE_ENV=cli node dist/cli.js", "start:http": "node dist/cli.js", "dev": "cross-env NODE_ENV=development tsup --watch", "dev:cli": "cross-env NODE_ENV=development tsup --watch -- --stdio", "lint": "eslint . --ext .ts", "format": "prettier --write \"src/**/*.ts\"", "inspect": "pnpx @modelcontextprotocol/inspector", "prepack": "pnpm build", "changeset": "changeset add", "version": "changeset version && git add -A", "beta:start": "changeset pre enter beta", "beta:end": "changeset pre exit", "beta:version": "changeset version && pnpm install --lockfile-only", "beta:publish": "changeset publish", "prerelease": "pnpm build", "release": "changeset publish && git push --follow-tags", "pub:release": "pnpm build && npm publish", "pub:release:beta": "pnpm build && npm publish --tag beta"}, "engines": {"node": ">=18.0.0"}, "packageManager": "pnpm@10.10.0", "repository": {"type": "git", "url": "git+https://github.com/GLips/Figma-Context-MCP.git"}, "homepage": "https://www.framelink.ai", "keywords": ["figma", "mcp", "typescript"], "author": "", "license": "MIT", "dependencies": {"@figma/rest-api-spec": "^0.24.0", "@modelcontextprotocol/sdk": "^1.10.2", "@types/yargs": "^17.0.33", "cross-env": "^7.0.3", "dotenv": "^16.4.7", "express": "^4.21.2", "js-yaml": "^4.1.0", "remeda": "^2.20.1", "yargs": "^17.7.2", "zod": "^3.24.2"}, "devDependencies": {"@changesets/changelog-github": "^0.5.1", "@changesets/cli": "^2.29.2", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/js-yaml": "^4.0.9", "@types/node": "^20.17.0", "@typescript-eslint/eslint-plugin": "^8.24.0", "@typescript-eslint/parser": "^8.24.0", "eslint": "^9.20.1", "eslint-config-prettier": "^10.0.1", "jest": "^29.7.0", "prettier": "^3.5.0", "ts-jest": "^29.2.5", "tsup": "^8.4.0", "tsx": "^4.19.2", "typescript": "^5.7.3"}}