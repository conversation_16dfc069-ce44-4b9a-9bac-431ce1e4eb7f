"""
Figma Normalized Extractor

Este módulo é responsável por extrair os dados dos componentes do Figma e gerar
JSONs normalizados no formato especificado, com filtro de visibilidade e
mapeamento de webcomponents.
"""

import json
import logging
from pathlib import Path
from typing import Dict, List, Any

from src.utils.file import sanitize_name
from src.utils.component_visibility_resolver import filter_visible_nodes

logger = logging.getLogger(__name__)


class FigmaNormalizedExtractor:
    """
    Extrator que gera JSONs normalizados do Figma com filtro de visibilidade
    e mapeamento de webcomponents.
    """

    def __init__(self, output_dir: str = "data/figma_extraction"):
        """
        Inicializa o extrator normalizado.
        
        Args:
            output_dir: Diretório para salvar componentes extraídos.
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self.webcomponent_mapper = None
        logger.debug(f"FigmaNormalizedExtractor inicializado. Diretório de saída: {self.output_dir}")

    def extract_normalized_component(self, figma_component_data: Dict[str, Any], components_dir: Path = None) -> Dict[str, Any]:
        """
        Extrai componente do Figma e gera JSON normalizado, removendo campos internos desnecessários.
        
        Args:
            figma_component_data: Dados do componente do Figma
            components_dir: Diretório de componentes para mapeamento (opcional)
            
        Returns:
            Dicionário com dados normalizados do componente
        """
        component_name = figma_component_data.get('name', 'Unknown')
        component_id = figma_component_data.get('id', 'unknown')
        logger.debug(f"Extraindo componente normalizado: {component_name}")
        
        # Verificar se é um ícone baseado no nome
        if self._is_icon_component(component_name):
            return self._extract_icon_component(figma_component_data)
        
        # Extrair dados essenciais
        normalized_data = {
            'id': component_id,
            'name': component_name,
            'type': figma_component_data.get('type', 'UNKNOWN'),
            'componentId': figma_component_data.get('componentId'),
            'visible': figma_component_data.get('visible', True),
            'css': self._extract_css_properties(figma_component_data),
            'props': self._extract_properties(figma_component_data)
        }
        
        # Remover campos com valores nulos ou vazios
        normalized_data = {k: v for k, v in normalized_data.items() if v is not None and v != ''}
        
        # Processar children recursivamente
        children = self._extract_children(figma_component_data, components_dir)
        if children:
            normalized_data['children'] = children
        
        # Processar components e componentSets (apenas se não vazios)
        if 'components' in figma_component_data or 'componentSets' in figma_component_data:
            components_data = self._process_components_and_componentsets(figma_component_data)
            if components_data:
                normalized_data.update(components_data)
        
        return normalized_data

    def _is_icon_component(self, component_name: str) -> bool:
        """
        Verifica se o componente é um ícone baseado no nome.
        
        Args:
            component_name: Nome do componente
            
        Returns:
            True se for um ícone, False caso contrário
        """
        name_lower = component_name.lower()
        return name_lower.startswith('icon-') or name_lower.startswith('ui-')

    def _extract_icon_component(self, figma_component_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extrai apenas as propriedades necessárias para ícones.
        
        Args:
            figma_component_data: Dados do componente do Figma
            
        Returns:
            Dicionário com propriedades mínimas do ícone
        """
        component_name = figma_component_data.get('name', 'Unknown')
        component_id = figma_component_data.get('id', 'unknown')
        
        # Extrair apenas CSS de dimensões
        css = {}
        bounding_box = figma_component_data.get('absoluteBoundingBox')
        if bounding_box:
            css['width'] = f"{bounding_box.get('width', 0)}px"
            css['height'] = f"{bounding_box.get('height', 0)}px"
        
        return {
            "id": component_id,
            "name": component_name,
            "type": "ICON",
            "componentId": figma_component_data.get('componentId'),
            "visible": figma_component_data.get('visible', True),
            "css": css
        }

    def _process_components_and_componentsets(self, figma_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Processa components e componentSets para criar relacionamentos e filtrar visibilidade.
        
        Args:
            figma_data: Dados do Figma
            
        Returns:
            Dicionário com components e componentSets processados
        """
        components = figma_data.get('components', {})
        component_sets = figma_data.get('componentSets', {})
        
        # Filtrar apenas componentes visíveis
        visible_components = {}
        for comp_id, comp_data in components.items():
            # Verificar se o componente é visível (se tem dados de visibilidade)
            if self._is_component_visible(comp_data):
                visible_components[comp_id] = comp_data
        
        # Coletar componentIds que estão sendo usados pelos children visíveis
        used_component_ids = self._collect_used_component_ids(figma_data)
        
        # Reorganizar componentSets para incluir apenas components relacionados e visíveis
        processed_component_sets = {}
        for set_id, set_data in component_sets.items():
            # Encontrar todos os components que pertencem a este componentSet
            related_components = {}
            for comp_id, comp_data in visible_components.items():
                if comp_data.get('componentSetId') == set_id:
                    # Verificar se este componentId está sendo usado pelos children visíveis
                    if comp_id in used_component_ids:
                        # Remover campo 'remote' dos components
                        comp_data_clean = comp_data.copy()
                        comp_data_clean.pop('remote', None)
                        related_components[comp_id] = comp_data_clean
            
            # Se há components relacionados e visíveis, adicionar ao componentSet
            if related_components:
                processed_set = set_data.copy()
                # Remover campo 'remote' do componentSet
                processed_set.pop('remote', None)
                processed_set['components'] = related_components
                processed_component_sets[set_id] = processed_set
        
        return {
            'components': {},  # Removido: components já estão dentro de cada componentSet
            'componentSets': processed_component_sets
        }

    def _collect_used_component_ids(self, figma_data: Dict[str, Any]) -> set:
        """
        Coleta todos os componentIds que estão sendo usados pelos children visíveis.
        
        Args:
            figma_data: Dados do Figma
            
        Returns:
            Set com os componentIds em uso
        """
        used_ids = set()
        
        def collect_from_node(node):
            # Verificar se o node é visível
            if not node.get('visible', True):
                return
            
            # Se é INSTANCE/COMPONENT, coletar o componentId
            if node.get('type') in ('INSTANCE', 'COMPONENT'):
                component_id = node.get('componentId')
                if component_id:
                    used_ids.add(component_id)
            
            # Recursivamente processar children
            for child in node.get('children', []):
                collect_from_node(child)
        
        # Processar o node raiz
        collect_from_node(figma_data)
        
        return used_ids

    def _is_component_visible(self, component_data: Dict[str, Any]) -> bool:
        """
        Verifica se um componente é visível baseado em suas propriedades.
        
        Args:
            component_data: Dados do componente
            
        Returns:
            True se visível, False caso contrário
        """
        # Por padrão, componentes são visíveis
        # Aqui você pode implementar lógica específica de visibilidade se necessário
        return True

    def _extract_css_properties(self, node_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extrai propriedades CSS do node, convertendo propriedades de layout do Figma em CSS real.
        
        Args:
            node_data: Dados do node do Figma
            
        Returns:
            Dicionário com propriedades CSS
        """
        css = {}
        
        # Dimensões básicas
        width = node_data.get('absoluteBoundingBox', {}).get('width')
        height = node_data.get('absoluteBoundingBox', {}).get('height')
        
        if width:
            css['width'] = f"{width}px"
        if height:
            css['height'] = f"{height}px"
        
        # Layout Mode (Flexbox)
        layout_mode = node_data.get('layoutMode')
        if layout_mode:
            if layout_mode == 'HORIZONTAL':
                css['display'] = 'flex'
                css['flexDirection'] = 'row'
            elif layout_mode == 'VERTICAL':
                css['display'] = 'flex'
                css['flexDirection'] = 'column'
        
        # Item Spacing (Gap)
        item_spacing = node_data.get('itemSpacing')
        if item_spacing is not None:
            css['gap'] = f"{item_spacing}px"
        
        # Layout Wrap
        layout_wrap = node_data.get('layoutWrap')
        if layout_wrap:
            if layout_wrap == 'WRAP':
                css['flexWrap'] = 'wrap'
            elif layout_wrap == 'NO_WRAP':
                css['flexWrap'] = 'nowrap'
        
        # Alignment (Primary Axis)
        primary_axis_align = node_data.get('primaryAxisAlignItems')
        if primary_axis_align:
            if primary_axis_align == 'MIN':
                css['justifyContent'] = 'flex-start'
            elif primary_axis_align == 'MAX':
                css['justifyContent'] = 'flex-end'
            elif primary_axis_align == 'CENTER':
                css['justifyContent'] = 'center'
            elif primary_axis_align == 'SPACE_BETWEEN':
                css['justifyContent'] = 'space-between'
        
        # Alignment (Counter Axis)
        counter_axis_align = node_data.get('counterAxisAlignItems')
        if counter_axis_align:
            if counter_axis_align == 'MIN':
                css['alignItems'] = 'flex-start'
            elif counter_axis_align == 'MAX':
                css['alignItems'] = 'flex-end'
            elif counter_axis_align == 'CENTER':
                css['alignItems'] = 'center'
            elif counter_axis_align == 'BASELINE':
                css['alignItems'] = 'baseline'
        
        # Constraints (Positioning)
        constraints = node_data.get('constraints', {})
        if constraints:
            vertical_constraint = constraints.get('vertical')
            horizontal_constraint = constraints.get('horizontal')
            
            if vertical_constraint == 'TOP':
                css['alignSelf'] = 'flex-start'
            elif vertical_constraint == 'BOTTOM':
                css['alignSelf'] = 'flex-end'
            elif vertical_constraint == 'CENTER':
                css['alignSelf'] = 'center'
            
            if horizontal_constraint == 'LEFT':
                css['justifySelf'] = 'flex-start'
            elif horizontal_constraint == 'RIGHT':
                css['justifySelf'] = 'flex-end'
            elif horizontal_constraint == 'CENTER':
                css['justifySelf'] = 'center'
        
        # Background color
        fills = node_data.get('fills', [])
        if fills and len(fills) > 0:
            fill = fills[0]
            if fill.get('type') == 'SOLID':
                color = fill.get('color', {})
                if color:
                    r = int(color.get('r', 0) * 255)
                    g = int(color.get('g', 0) * 255)
                    b = int(color.get('b', 0) * 255)
                    a = color.get('a', 1)
                    css['backgroundColor'] = f"rgba({r}, {g}, {b}, {a})"
        
        # Border radius
        corner_radius = node_data.get('cornerRadius')
        if corner_radius and corner_radius > 0:
            css['borderRadius'] = f"{corner_radius}px"
        
        # Effects (shadows)
        effects = node_data.get('effects', [])
        if effects:
            shadow_effects = [e for e in effects if e.get('type') == 'DROP_SHADOW']
            if shadow_effects:
                shadow = shadow_effects[0]
                color = shadow.get('color', {})
                offset = shadow.get('offset', {})
                radius = shadow.get('radius', 0)
                
                r = int(color.get('r', 0) * 255)
                g = int(color.get('g', 0) * 255)
                b = int(color.get('b', 0) * 255)
                a = color.get('a', 1)
                
                x = offset.get('x', 0)
                y = offset.get('y', 0)
                
                css['boxShadow'] = f"{x}px {y}px {radius}px rgba({r}, {g}, {b}, {a})"
        
        # Padding (from layout)
        padding_top = node_data.get('paddingTop', 0)
        padding_right = node_data.get('paddingRight', 0)
        padding_bottom = node_data.get('paddingBottom', 0)
        padding_left = node_data.get('paddingLeft', 0)
        
        if any([padding_top, padding_right, padding_bottom, padding_left]):
            css['padding'] = f"{padding_top}px {padding_right}px {padding_bottom}px {padding_left}px"
        
        return css

    def _extract_properties(self, node_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extrai propriedades do componente, removendo propriedades internas do Figma.
        
        Args:
            node_data: Dados do node do Figma
            
        Returns:
            Dicionário com propriedades limpas
        """
        props = {}
        
        # Component properties (estados do design system)
        component_properties = node_data.get('componentProperties', {})
        for prop_name, prop_data in component_properties.items():
            # Remover propriedades internas do Figma
            if prop_name.startswith('✏️'):
                # Propriedades de conteúdo (Label, Content, Hint Text, etc.)
                prop_value = prop_data.get('value')
                if prop_value is not None:
                    props[prop_name] = prop_value
            elif not prop_name.startswith('Is ') and not prop_name.startswith('Has '):
                # Propriedades de estado que não são internas
                prop_value = prop_data.get('value')
                if prop_value is not None:
                    props[prop_name] = prop_value
        
        # Text content (apenas se não for redundante)
        characters = node_data.get('characters')
        if characters and not any('text' in key.lower() for key in props.keys()):
            props['text'] = characters
        
        # Component set properties (estados padrão)
        if node_data.get('type') == 'INSTANCE':
            props['state'] = 'Default'
            props['disabled'] = False
        
        # Remover propriedades com valores padrão desnecessários
        props_to_remove = []
        for key, value in props.items():
            if value in ['False', False, 'Non-validation', 'Default']:
                props_to_remove.append(key)
        
        for key in props_to_remove:
            del props[key]
        
        return props

    def _extract_children(self, node_data: Dict[str, Any], components_dir: Path = None) -> List[Dict[str, Any]]:
        """
        Extrai filhos do node recursivamente aplicando filtro de visibilidade.
        
        Args:
            node_data: Dados do node do Figma
            components_dir: Diretório de componentes para mapeamento (opcional)
            
        Returns:
            Lista de filhos normalizados e visíveis
        """
        children = []
        children_data = node_data.get('children', [])
        
        for child in children_data:
            # Aplicar filtro de visibilidade
            filtered_child = filter_visible_nodes(child)
            if filtered_child is None:
                continue  # Child é invisível, pular
            
            # Se for INSTANCE/COMPONENT, verificar visibilidade do referenciado
            if components_dir and child.get('type') in ('INSTANCE', 'COMPONENT'):
                from src.utils.component_visibility_resolver import is_component_instance_visible
                if not is_component_instance_visible(child, components_dir):
                    continue
            
            # Extrair dados normalizados do filho
            child_normalized = self.extract_normalized_component(filtered_child, components_dir)
            children.append(child_normalized)
        
        return children

    def save_normalized_component(self, component_data: Dict[str, Any], project_name: str, node_name: str) -> Path:
        """
        Salva componente normalizado em arquivo JSON com estrutura de pastas organizada.
        
        Args:
            component_data: Dados normalizados do componente
            project_name: Nome do projeto/arquivo Figma
            node_name: Nome do node selecionado
            
        Returns:
            Caminho do arquivo salvo
        """
        # Normalizar nomes
        project_name_normalized = sanitize_name(project_name)
        node_name_normalized = sanitize_name(node_name)
        
        # Criar estrutura de pastas: projeto/node/
        output_path = self.output_dir / project_name_normalized / node_name_normalized
        output_path.mkdir(parents=True, exist_ok=True)
        
        # Criar pasta components
        components_path = output_path / "components"
        components_path.mkdir(exist_ok=True)
        
        # Separar children do componente principal
        children = component_data.pop('children', [])
        
        # Salvar componente principal (sem children)
        filename = f"{node_name_normalized}.json"
        file_path = output_path / filename
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(component_data, f, indent=2, ensure_ascii=False)
        
        logger.debug(f"Componente principal salvo: {file_path}")
        
        # Salvar children separadamente na pasta components
        for i, child in enumerate(children):
            child_name = child.get('name', f'child_{i}')
            child_name_normalized = sanitize_name(child_name)
            child_filename = f"{child_name_normalized}.json"
            child_file_path = components_path / child_filename
            
            with open(child_file_path, 'w', encoding='utf-8') as f:
                json.dump(child, f, indent=2, ensure_ascii=False)
            
            logger.debug(f"Child salvo: {child_file_path}")
        
        return file_path

    def extract_and_save_components(self, figma_data: Dict[str, Any], project_name: str, node_name: str) -> List[Path]:
        """
        Extrai e salva todos os componentes de primeiro nível de um documento Figma.
        
        Args:
            figma_data: Dados do documento Figma
            project_name: Nome do projeto/arquivo Figma
            node_name: Nome do node selecionado
            
        Returns:
            Lista de caminhos dos arquivos salvos
        """
        saved_files = []
        
        # Extrair componentes de primeiro nível
        children = figma_data.get('children', [])
        
        for child in children:
            try:
                # Extrair componente normalizado
                component_data = self.extract_normalized_component(child)
                
                # Salvar arquivo
                file_path = self.save_normalized_component(component_data, project_name, node_name)
                saved_files.append(file_path)
                
                logger.debug(f"✅ Componente extraído: {child.get('name', 'Unknown')}")
                
            except Exception as e:
                logger.error(f"Erro ao extrair componente {child.get('name', 'Unknown')}: {e}")
        
        return saved_files 