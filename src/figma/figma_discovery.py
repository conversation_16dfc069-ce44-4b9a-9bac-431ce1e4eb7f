"""
Descobridor inteligente de nodes do Figma.

Este módulo implementa estratégias para descobrir nodes em arquivos Figma
sem baixar o arquivo completo, baseado na estratégia do MCP server.
"""

import logging
import requests
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field
from src.figma.figma_type_mapper import NodeType, classify_node_type, generate_node_description, has_layout_properties, has_content

logger = logging.getLogger(__name__)


@dataclass
class DiscoveredNode:
    """
    Representa um node descoberto.
    Inclui campo raw_data para armazenar todos os metadados do node Figma,
    permitindo extração completa e flexível de propriedades, tokens, variáveis, etc.
    """
    id: str
    name: str
    type: str
    node_type: NodeType
    level: int
    parent_id: Optional[str] = None
    parent_name: Optional[str] = None
    children_count: int = 0
    has_layout: bool = False
    has_content: bool = False
    estimated_complexity: str = "low"  # low, medium, high
    description: str = ""
    raw_data: Dict[str, Any] = field(default_factory=dict)  # All raw node data from Figma


class FigmaDiscovery:
    """
    Descobridor inteligente de nodes do Figma.
    
    Implementa estratégias otimizadas para descobrir nodes específicos
    sem baixar arquivos completos de 50MB+.
    """
    
    def __init__(self, api_token: str):
        """
        Inicializa o descobridor.
        
        Args:
            api_token: Token da API do Figma
        """
        self.api_token = api_token
        self.base_url = "https://api.figma.com/v1"
        self.headers = {
            "X-Figma-Token": api_token,
            "Content-Type": "application/json"
        }
        self.session = requests.Session()
        self.session.headers.update(self.headers)
    
    def discover_file_structure(
        self, 
        file_key: str, 
        max_depth: int = 3,
        include_components_only: bool = False
    ) -> List[DiscoveredNode]:
        """
        Descobre a estrutura de um arquivo Figma de forma otimizada.
        
        Args:
            file_key: Chave do arquivo Figma
            max_depth: Profundidade máxima de descoberta
            include_components_only: Se deve incluir apenas componentes
            
        Returns:
            Lista de nodes descobertos
        """
        logger.debug(f"Descobrindo estrutura do arquivo: {file_key}")
        
        discovered_nodes = []
        
        try:
            # Etapa 1: Descobrir páginas (depth=1)
            pages = self._discover_pages(file_key)
            discovered_nodes.extend(pages)
            
            # Etapa 2: Para cada página, descobrir elementos principais
            for page in pages:
                if page.node_type == NodeType.CANVAS:
                    elements = self._discover_page_elements(
                        file_key, 
                        page.id, 
                        page.name,
                        max_depth - 1,
                        include_components_only
                    )
                    discovered_nodes.extend(elements)
            
            # Etapa 3: Analisar e classificar nodes
            self._analyze_nodes(discovered_nodes)
            
            logger.debug(f"Descobertos {len(discovered_nodes)} nodes")
            return discovered_nodes
            
        except Exception as e:
            logger.error(f"Erro na descoberta: {e}")
            raise

    def discover_page_nodes(
        self,
        file_key: str,
        page_id: str,
        max_depth: int = 3
    ) -> List[DiscoveredNode]:
        """
        Descobre nodes em uma página específica.

        Args:
            file_key: Chave do arquivo Figma
            page_id: ID da página específica
            max_depth: Profundidade máxima de descoberta

        Returns:
            Lista de nodes descobertos na página
        """
        logger.debug(f"Descobrindo nodes na página: {page_id}")

        try:
            # Descobrir elementos da página específica
            elements = self._discover_page_elements(
                file_key,
                page_id,
                f"Page_{page_id}",
                max_depth,
                include_components_only=False
            )

            # Analisar e classificar nodes
            self._analyze_nodes(elements)

            logger.debug(f"Descobertos {len(elements)} nodes na página")
            return elements

        except Exception as e:
            logger.error(f"Erro na descoberta da página: {e}")
            raise
    
    def _discover_pages(self, file_key: str) -> List[DiscoveredNode]:
        """Descobre páginas do arquivo."""
        logger.debug("Descobrindo páginas...")
        
        url = f"{self.base_url}/files/{file_key}?depth=1"
        
        try:
            response = self.session.get(url)
            response.raise_for_status()
            data = response.json()
            
            pages = []
            
            if 'document' in data and 'children' in data['document']:
                for i, page_data in enumerate(data['document']['children']):
                    page = DiscoveredNode(
                        id=page_data.get('id', ''),
                        name=page_data.get('name', f'Página {i+1}'),
                        type=page_data.get('type', 'CANVAS'),
                        node_type=NodeType.CANVAS,
                        level=0,
                        children_count=len(page_data.get('children', [])),
                        description=f"Canvas com {len(page_data.get('children', []))} elementos"
                    )
                    pages.append(page)
            
            logger.debug(f"Encontradas {len(pages)} páginas")
            return pages
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Erro ao descobrir páginas: {e}")
            raise
    
    def _discover_page_elements(
        self, 
        file_key: str, 
        page_id: str, 
        page_name: str,
        max_depth: int,
        include_components_only: bool
    ) -> List[DiscoveredNode]:
        """Descobre elementos de uma página específica."""
        
        if max_depth <= 0:
            return []
        
        # SEMPRE usar depth=1 para consultas rápidas
        # O max_depth é usado apenas para controlar a recursão, não a profundidade da API
        url = f"{self.base_url}/files/{file_key}/nodes?ids={page_id}&depth=1"
        
        try:
            response = self.session.get(url)
            response.raise_for_status()
            data = response.json()
            
            elements = []
            
            if 'nodes' in data and page_id in data['nodes']:
                node_data = data['nodes'][page_id]
                if 'document' in node_data and 'children' in node_data['document']:
                    elements = self._process_children(
                        node_data['document']['children'],
                        level=1,
                        parent_id=page_id,
                        parent_name=page_name,
                        max_depth=max_depth,
                        include_components_only=include_components_only
                    )
            
            logger.debug(f"Encontrados {len(elements)} elementos na página {page_name}")
            return elements
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Erro ao descobrir elementos da página {page_name}: {e}")
            return []
    
    def _process_children(
        self,
        children: List[Any],
        level: int,
        parent_id: str,
        parent_name: str,
        max_depth: int,
        include_components_only: bool
    ) -> List[DiscoveredNode]:
        """
        Processa children de um node recursivamente e normaliza para estrutura pronta para geração de código.
        """
        elements = []

        # --- Nível especial de filtragem ---
        if level == 1:
            filtered = [c for c in children if classify_node_type(c) in [NodeType.SECTION, NodeType.FRAME]]
            if filtered:
                children_to_process = filtered
            else:
                children_to_process = children
        else:
            children_to_process = children

        # Filtrar nodes visíveis antes de processar
        visible_children = [c for c in children_to_process if c.get('visible', True)]

        for child_data in visible_children:
            node_type = classify_node_type(child_data)

            # Filtrar apenas componentes se solicitado
            if include_components_only and node_type not in [NodeType.COMPONENT, NodeType.INSTANCE, NodeType.FRAME]:
                continue

            # --- Normalização de propriedades CSS ---
            css = {}
            abs_box = child_data.get('absolute_bounding_box') or child_data.get('absoluteBoundingBox')
            if abs_box:
                if 'width' in abs_box:
                    css['width'] = f"{abs_box['width']}px"
                if 'height' in abs_box:
                    css['height'] = f"{abs_box['height']}px"
            layout_mode = child_data.get('layout_mode') or child_data.get('layoutMode')
            if layout_mode:
                css['display'] = 'flex'
                css['flexDirection'] = 'row' if layout_mode.upper() == 'HORIZONTAL' else 'column'
            if child_data.get('item_spacing') is not None:
                css['gap'] = f"{child_data['item_spacing']}px"
            # Normalização de padding - só aplicar se houver pelo menos um valor definido
            padding_left = child_data.get('padding_left')
            padding_right = child_data.get('padding_right')
            padding_top = child_data.get('padding_top')
            padding_bottom = child_data.get('padding_bottom')
            
            # Só aplicar padding se pelo menos um valor estiver definido
            if any(p is not None for p in [padding_left, padding_right, padding_top, padding_bottom]):
                padding_parts = []
                
                # Para valores não definidos, usar valores padrão do Design System
                # ou deixar que o CSS do Design System defina
                if padding_top is not None:
                    padding_parts.append(f"{padding_top}px")
                else:
                    padding_parts.append("auto")  # Deixar o Design System definir
                
                if padding_right is not None:
                    padding_parts.append(f"{padding_right}px")
                else:
                    padding_parts.append("auto")
                
                if padding_bottom is not None:
                    padding_parts.append(f"{padding_bottom}px")
                else:
                    padding_parts.append("auto")
                
                if padding_left is not None:
                    padding_parts.append(f"{padding_left}px")
                else:
                    padding_parts.append("auto")
                
                # Só aplicar se todos os valores não forem "auto"
                if not all(p == "auto" for p in padding_parts):
                    # Substituir "auto" por valores padrão do Design System
                    final_padding = []
                    for p in padding_parts:
                        if p == "auto":
                            final_padding.append("0px")  # Valor padrão seguro
                        else:
                            final_padding.append(p)
                    
                    css['padding'] = ' '.join(final_padding)
            if child_data.get('corner_radius') is not None:
                css['borderRadius'] = f"{child_data['corner_radius']}px"
            if child_data.get('backgroundColor'):
                color = child_data['backgroundColor']
                css['backgroundColor'] = f"rgba({color['r']*255:.0f},{color['g']*255:.0f},{color['b']*255:.0f},{color['a']})"
            if child_data.get('opacity') is not None:
                css['opacity'] = child_data['opacity']
            # TODO: boxShadow, border, etc.

            # --- Normalização de props ---
            props = {}
            # Para INSTANCE, COMPONENT, TEXT, etc.
            if node_type in [NodeType.INSTANCE, NodeType.COMPONENT]:
                # Tenta pegar propriedades relevantes
                if 'component_properties' in child_data:
                    for k, v in child_data['component_properties'].items():
                        if isinstance(v, dict) and 'value' in v:
                            props[k] = v['value']
                        else:
                            props[k] = v
            # Para TEXT, pega characters
            if node_type == NodeType.TEXT:
                props['text'] = child_data.get('characters', '')
                if 'style' in child_data:
                    props['style'] = child_data['style']

            # --- Recursão para children ---
            children = []
            if max_depth > 1 and 'children' in child_data and child_data['children']:
                # Filtrar children visíveis ANTES da recursão
                visible_grandchildren = [gc for gc in child_data['children'] if gc.get('visible', True)]
                if len(visible_grandchildren) <= 20:
                    children = self._process_children(
                        visible_grandchildren,
                        level + 1,
                        child_data.get('id', ''),
                        child_data.get('name', ''),
                        max_depth - 1,
                        include_components_only
                    )

            # --- Monta node normalizado ---
            discovered_node = DiscoveredNode(
                id=child_data.get('id', ''),
                name=child_data.get('name', ''),
                type=child_data.get('type', ''),
                node_type=node_type,
                level=level,
                parent_id=parent_id,
                parent_name=parent_name,
                children_count=len(child_data.get('children', [])) if 'children' in child_data else 0,
                has_layout=has_layout_properties(child_data),
                has_content=has_content(child_data),
                estimated_complexity=self._estimate_complexity(child_data),
                description=generate_node_description(child_data, node_type),
                raw_data=child_data
            )
            elements.append(discovered_node)

        return elements
    
    
    def _estimate_complexity(self, node_data: Dict[str, Any]) -> str:
        """Estima a complexidade de um node."""
        complexity_score = 0
        
        # Use utility functions imported from figma_type_mapper
        if has_layout_properties(node_data):
            complexity_score += 2
        
        if has_content(node_data):
            complexity_score += 1
        
        children_count = len(node_data.get('children', []))
        if children_count > 10:
            complexity_score += 3
        elif children_count > 5:
            complexity_score += 2
        elif children_count > 0:
            complexity_score += 1
        
        # Classificar complexidade
        if complexity_score >= 5:
            return "high"
        elif complexity_score >= 3:
            return "medium"
        else:
            return "low"
    
    
    def _analyze_nodes(self, nodes: List[DiscoveredNode]) -> None:
        """Analisa e enriquece informações dos nodes descobertos."""
        logger.debug("Analisando nodes descobertos...")
        
        # Estatísticas
        by_type = {}
        by_complexity = {"low": 0, "medium": 0, "high": 0}
        
        for node in nodes:
            # Contar por tipo
            type_name = node.node_type.value
            by_type[type_name] = by_type.get(type_name, 0) + 1
            
            # Contar por complexidade
            by_complexity[node.estimated_complexity] += 1
        
        logger.debug("Estatísticas dos nodes:")
        for node_type, count in by_type.items():
            logger.debug(f"  {node_type}: {count}")
        
        logger.debug("Complexidade:")
        for complexity, count in by_complexity.items():
            logger.debug(f"  {complexity}: {count}")
    
    def discover_node_children(
        self,
        file_key: str,
        node_id: str,
        max_depth: int = 1
    ) -> List[DiscoveredNode]:
        """
        Descobre os filhos de um node específico.

        Args:
            file_key: Chave do arquivo Figma
            node_id: ID do node pai
            max_depth: Profundidade máxima de descoberta

        Returns:
            Lista de nodes filhos descobertos
        """
        logger.debug(f"Descobrindo filhos do node: {node_id}")

        try:
            # Usar a API para obter dados do node específico
            url = f"{self.base_url}/files/{file_key}/nodes?ids={node_id}&depth=1"
            
            response = self.session.get(url)
            response.raise_for_status()
            data = response.json()
            
            if not data.get('nodes') or node_id not in data['nodes']:
                logger.warning(f"Node {node_id} não encontrado")
                return []
            
            node_data = data['nodes'][node_id]
            document = node_data.get('document', {})
            
            # Processar filhos do node
            children = document.get('children', [])
            discovered_children = self._process_children(
                children=children,
                level=1,  # Nível dos filhos
                parent_id=node_id,
                parent_name=document.get('name', 'Unknown'),
                max_depth=max_depth,
                include_components_only=False
            )
            
            # Analisar e classificar nodes
            self._analyze_nodes(discovered_children)
            
            logger.debug(f"Descobertos {len(discovered_children)} filhos para o node {node_id}")
            return discovered_children
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Erro ao descobrir filhos do node {node_id}: {e}")
            return []
        except Exception as e:
            logger.error(f"Erro inesperado ao descobrir filhos do node {node_id}: {e}")
            return []

    def get_file_info(self, file_key: str) -> Dict[str, Any]:
        """Obtém informações básicas do arquivo."""
        url = f"{self.base_url}/files/{file_key}?depth=1"
        
        try:
            response = self.session.get(url)
            response.raise_for_status()
            data = response.json()
            
            return {
                'name': data.get('name', 'Arquivo sem nome'),
                'lastModified': data.get('lastModified', ''),
                'thumbnailUrl': data.get('thumbnailUrl', ''),
                'version': data.get('version', ''),
                'role': data.get('role', ''),
                'editorType': data.get('editorType', '')
            }
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Erro ao obter informações do arquivo: {e}")
            raise

    def get_node_details(self, file_key: str, node_id: str) -> Dict[str, Any]:
        """
        Obtém detalhes completos de um node específico.

        Args:
            file_key: Chave do arquivo Figma
            node_id: ID do node

        Returns:
            Dados completos do node
        """
        url = f"{self.base_url}/files/{file_key}/nodes?ids={node_id}"

        try:
            response = self.session.get(url)
            response.raise_for_status()
            return response.json()

        except requests.exceptions.RequestException as e:
            logger.error(f"Erro ao obter detalhes do node {node_id}: {e}")
            raise
