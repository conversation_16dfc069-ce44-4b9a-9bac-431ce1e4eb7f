#!/usr/bin/env python3
"""
Script para listar e verificar prompts disponíveis
"""

import sys
from pathlib import Path

# Adicionar o diretório raiz do projeto ao sys.path
sys.path.append(str(Path(__file__).parent.parent.parent))

from src.utils.prompt_loader import PromptLoader


def main():
    """Lista todos os prompts disponíveis."""
    
    print("🎯 Prompts Centralizados - Sistema de Geração de Código")
    print("=" * 60)
    
    try:
        loader = PromptLoader()
        available = loader.list_available_prompts()
        
        print("\n📝 System Prompts Disponíveis:")
        for framework in available["system_prompts"]:
            print(f"  - {framework}")
            
        print("\n💬 User Prompts Disponíveis:")
        for template in available["user_prompts"]:
            print(f"  - {template}")
            
        print("\n🛠️ Framework Específicos:")
        for framework in available["framework_specific"]:
            print(f"  - {framework}")
        
        print("\n" + "=" * 60)
        print("📁 Localização: ai_prompts/prompts.yaml")
        print("🔧 Loader: src/utils/prompt_loader.py")
        
        # Testar carregamento de um prompt
        print("\n🧪 Teste de Carregamento:")
        angular_prompt = loader.get_system_prompt("angular")
        print(f"✅ System prompt Angular: {len(angular_prompt)} caracteres")
        
        react_prompt = loader.get_system_prompt("react")
        print(f"✅ System prompt React: {len(react_prompt)} caracteres")
        
        user_prompt = loader.get_user_prompt(
            "component_generation",
            framework="angular",
            component_name="test-component",
            component_name_formatted="TestComponent",
            figma_context="[dados do figma]",
            design_system_context="[contexto do design system]"
        )
        print(f"✅ User prompt: {len(user_prompt)} caracteres")
        
    except Exception as e:
        print(f"❌ Erro: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
