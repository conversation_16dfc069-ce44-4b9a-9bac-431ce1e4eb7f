"""
Utilitário para carregar configurações de projeto de arquivos YAML/JSON.
"""

import yaml
import json
from typing import Dict, Any, Optional
from pathlib import Path
# Removido o import global de StorybookVersionDetector e get_storybook_config_from_yaml


class ConfigLoader:
    """Carregador de configurações de projeto."""
    
    def __init__(self, config_file: Optional[str] = None):
        """
        Inicializa o carregador de configurações.
        
        Args:
            config_file: Caminho para o arquivo de configuração. 
                        Se None, procura por project_config.yaml ou project_config.json na raiz.
        """
        self.config_file = config_file
        self.config_data = None
        
    def load_config(self) -> Dict[str, Any]:
        """
        Carrega as configurações do arquivo.
        
        Returns:
            Dicionário com as configurações carregadas.
            
        Raises:
            FileNotFoundError: Se o arquivo de configuração não for encontrado.
            ValueError: Se o arquivo não puder ser parseado.
        """
        if self.config_data is not None:
            return self.config_data
            
        config_path = self._find_config_file()
        
        if not config_path.exists():
            raise FileNotFoundError(
                f"Arquivo de configuração não encontrado: {config_path}\n"
                f"Crie um arquivo project_config.yaml ou project_config.json na raiz do projeto."
            )
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                if config_path.suffix.lower() == '.yaml' or config_path.suffix.lower() == '.yml':
                    self.config_data = yaml.safe_load(f)
                elif config_path.suffix.lower() == '.json':
                    self.config_data = json.load(f)
                else:
                    raise ValueError(f"Formato de arquivo não suportado: {config_path.suffix}")
                    
        except (yaml.YAMLError, json.JSONDecodeError) as e:
            raise ValueError(f"Erro ao parsear arquivo de configuração: {e}")
            
        return self.config_data
    
    def _find_config_file(self) -> Path:
        """
        Encontra o arquivo de configuração.
        
        Returns:
            Path para o arquivo de configuração.
        """
        if self.config_file:
            return Path(self.config_file)
            
        # Procura na raiz do projeto
        root_dir = Path(__file__).parent.parent.parent.parent
        
        # Ordem de prioridade: YAML primeiro, depois JSON
        for filename in ['project_config.yaml', 'project_config.yml', 'project_config.json']:
            config_path = root_dir / filename
            if config_path.exists():
                return config_path
                
        # Se não encontrou, retorna o padrão YAML
        return root_dir / 'project_config.yaml'
    
    def get_figma_config(self) -> Dict[str, str]:
        """
        Obtém as configurações específicas do Figma.
        
        Returns:
            Dicionário com file_key e token.
            
        Raises:
            KeyError: Se as configurações obrigatórias não estiverem presentes.
        """
        config = self.load_config()
        
        figma_config = config.get('figma', {})
        
        required_keys = ['file_key', 'token']
        missing_keys = [key for key in required_keys if key not in figma_config]
        
        if missing_keys:
            raise KeyError(
                f"Configurações obrigatórias do Figma não encontradas: {missing_keys}\n"
                f"Verifique o arquivo de configuração."
            )
            
        return {
            'file_key': figma_config['file_key'],
            'token': figma_config['token'],
            'max_exploration_depth': figma_config.get('max_exploration_depth')
        }
    
    def get_processing_config(self) -> Dict[str, Any]:
        """
        Obtém as configurações de processamento.
        
        Returns:
            Dicionário com configurações de processamento.
        """
        
        default_processing = {
            'max_depth': 3,
            'components_only': False
        }
        
        return default_processing
    
    def get_generation_config(self) -> Dict[str, Any]:
        """
        Obtém as configurações de geração de código.
        
        Returns:
            Dicionário com configurações de geração.
        """
        config = self.load_config()
        
        default_generation = {
            'framework': 'angular',
        }
        
        generation_config = config.get('generation', {})
        default_generation.update(generation_config)
        
        return default_generation
    
    def get_output_config(self) -> Dict[str, Any]:
        """
        Obtém as configurações de saída.
        
        Returns:
            Dicionário com configurações de saída.
        """
        config = self.load_config()
        
        default_output = {
            'base_dir': 'data/output',
            'figma_extraction_path': "data/figma_discovery",
            'design_system_path': "data/design_system"
        }
        
        output_config = config.get('output', {})
        default_output.update(output_config)
        
        return default_output
    
    def get_design_system_config(self) -> Dict[str, Any]:
        """
        Obtém as configurações do Design System.
        
        Returns:
            Dicionário com configurações do Design System.
        """
        config = self.load_config()
        
        # Configurações padrão do Design System (genéricas)
        default_ds_config = {
            'slug': 'meu_design_system',  # Deve ser configurado no YAML
            'name': 'Meu Design System',  # Deve ser configurado no YAML
            'storybook': {
                'base_url': '',  # Deve ser configurado no YAML
                'version': 'latest',
                'auto_detect_version': True,
                'components_path': '/?path=/docs/designsystem-components-',
                'docs_path': '/?path=/docs/design-system-'
            },
            'extraction': {
                'headless': True,
                'timeout': 30,
                'wait_time': 2,
                'max_retries': 3
            },
            'cache': {
                'update_interval_hours': 24,
                'force_update': False,
                'cache_dir': 'data/design_system'
            },
            'integration': {
                'auto_map_components': True,
                'generate_react': True,
                'generate_angular': True,
                'validate_code': True
            }
        }
        
        ds_config = config.get('design_system', {})
        
        # Mesclar configurações
        default_ds_config['slug'] = ds_config.get('slug', default_ds_config['slug'])
        default_ds_config['name'] = ds_config.get('name', default_ds_config['name'])

        if 'storybook' in ds_config:
            default_ds_config['storybook'].update(ds_config['storybook'])
        
        if 'cache' in ds_config:
            default_ds_config['cache'].update(ds_config['cache'])
        
        return default_ds_config
    
    def get_ai_config(self) -> Dict[str, Any]:
        """
        Obtém as configurações de IA.
        
        Returns:
            Dicionário com configurações de IA.
        """
        config = self.load_config()
        
        default_ai_config = {
            'provider': 'flow_api',
            'model': {
                'default': 'anthropic.claude-4-sonnet',
                'analysis': 'gemini-2.5-flash',
                'generation': 'anthropic.claude-4-sonnet',
                'backup': 'gpt-4.1'
            },
            'output_tokens': 16000,
            'temperature': 0.1
        }
        
        # Carregar configurações do arquivo YAML
        ai_config = config.get('ai', {})
        
        # Mesclar configurações, preservando a estrutura aninhada
        if 'model' in ai_config:
            default_ai_config['model'].update(ai_config['model'])
        
        # Atualizar outras configurações
        for key, value in ai_config.items():
            if key != 'model':  # model já foi tratado acima
                default_ai_config[key] = value
        
        return default_ai_config
    
    def get_storybook_url(self) -> str:
        """
        Obtém a URL base do Storybook a partir da configuração.
        A detecção de versão e construção da URL final foram movidas
        para o script de extração para evitar redundância.
        
        Returns:
            URL base do Storybook
        """
        config = self.load_config()
        ds_config = config.get('design_system', {})
        storybook_config = ds_config.get('storybook', {})
        
        base_url = storybook_config.get('base_url')
        if not base_url:
            raise ValueError("A 'base_url' do Storybook não está configurada no seu project_config.yaml")
            
        return base_url.rstrip('/')
    
    def validate_design_system_config(self) -> bool:
        """
        Valida se as configurações do Design System estão corretas.
        
        Returns:
            True se as configurações estiverem válidas
        """
        try:
            ds_config = self.get_design_system_config()
            storybook_config = ds_config['storybook']
            
            # Validar URL base
            base_url = storybook_config['base_url']
            if not base_url.startswith('http'):
                return False
            
            # Validar timeout
            timeout = ds_config['extraction']['timeout']
            if timeout <= 0:
                return False
            
            return True
            
        except Exception:
            return False


def load_project_config(config_file: Optional[str] = None) -> ConfigLoader:
    """
    Função de conveniência para carregar configurações de projeto.
    
    Args:
        config_file: Caminho opcional para o arquivo de configuração.
        
    Returns:
        Instância do ConfigLoader com configurações carregadas.
    """
    return ConfigLoader(config_file)
