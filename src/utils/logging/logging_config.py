"""
Configuração centralizada de logging para o projeto.

Este módulo configura logging para console e arquivo, com rotação automática
e diferentes níveis de log para desenvolvimento e produção.
"""

import logging
import logging.handlers
from pathlib import Path
from datetime import datetime
from typing import Optional
import sys


def setup_logging(
    log_level: str = "INFO",
    log_to_file: bool = True,
    log_file: Optional[str] = None,
    script_name: Optional[str] = None
) -> None:
    """
    Configura o sistema de logging.
    
    Args:
        log_level: Nível de log (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_to_file: Se deve salvar logs em arquivo
        log_file: Nome do arquivo de log (opcional)
        script_name: Nome do script para personalizar o arquivo de log
    """
    # Criar pasta logs se não existir
    logs_dir = Path("data/logs")
    logs_dir.mkdir(parents=True, exist_ok=True)
    
    # Configurar formato do log
    log_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    date_format = "%Y-%m-%d %H:%M:%S"
    
    # Configurar handler para console (apenas logs principais)
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)  # Console sempre INFO ou superior
    console_formatter = logging.Formatter("%(levelname)s - %(message)s")
    console_handler.setFormatter(console_formatter)
    
    # Configurar handler para arquivo (todos os logs)
    if log_to_file:
        if log_file is None:
            # Gerar nome baseado na data/hora e nome do script
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            if script_name:
                log_file = f"data/logs/{script_name}_{timestamp}.log"
            else:
                log_file = f"data/logs/design_system_extraction_{timestamp}.log"
        else:
            # Garantir que o arquivo seja salvo na pasta data/logs
            if not log_file.startswith("data/logs/"):
                log_file = f"data/logs/{log_file}"
        
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)  # Arquivo sempre com DEBUG para capturar todos os logs
        file_formatter = logging.Formatter(log_format, date_format)
        file_handler.setFormatter(file_formatter)
    
    # Configurar logger raiz
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.DEBUG)  # Sempre DEBUG para capturar todos os logs
    
    # Limpar handlers existentes
    root_logger.handlers.clear()
    
    # Adicionar handlers
    root_logger.addHandler(console_handler)
    if log_to_file:
        root_logger.addHandler(file_handler)
        print(f"Logs serão salvos em: {log_file}")
    
    # Configurar loggers específicos
    loggers_to_configure = [
        "src.design_system.storybook_scraper",
        "src.design_system.integration", 
        "src.utils.config_loader",
        "src.scripts.storybook_extract",
        "src.scripts.figma_extract",
        "src.scripts.code_generator",
        "src.scripts.code_generator_main",
        "src.scripts.integrated_extraction",
        "src.scripts.run_complete_pipeline",
        "src.figma",
        "src.generators",
        "src.utils",
    ]
    
    for logger_name in loggers_to_configure:
        logger = logging.getLogger(logger_name)
        logger.setLevel(getattr(logging, log_level.upper()))
        logger.propagate = True

    # Filtrar logs de Selenium e outras bibliotecas externas
    external_loggers = [
        "selenium",
        "urllib3",
        "requests",
        "webdriver",
        "WDM",
        "flow_api",
    ]

    for logger_name in external_loggers:
        logger = logging.getLogger(logger_name)
        logger.setLevel(logging.WARNING)  # Apenas warnings e erros
        logger.propagate = False  # Não propagar para o arquivo


def get_logger(name: str) -> logging.Logger:
    """
    Obtém um logger configurado.
    
    Args:
        name: Nome do logger
        
    Returns:
        Logger configurado
    """
    return logging.getLogger(name)


def log_function_call(func_name: str, args: dict = None, result: str = None) -> None:
    """
    Log padronizado para chamadas de função.
    
    Args:
        func_name: Nome da função
        args: Argumentos da função
        result: Resultado da função
    """
    logger = get_logger("function_calls")
    
    if args:
        logger.debug(f"Chamando {func_name} com args: {args}")
    else:
        logger.debug(f"Chamando {func_name}")
    
    if result:
        logger.debug(f"{func_name} retornou: {result}")


def log_performance(operation: str, duration: float, details: dict = None) -> None:
    """
    Log padronizado para métricas de performance.
    
    Args:
        operation: Nome da operação
        duration: Duração em segundos
        details: Detalhes adicionais
    """
    logger = get_logger("performance")
    
    message = f"Performance - {operation}: {duration:.2f}s"
    if details:
        message += f" | Detalhes: {details}"
    
    logger.info(message)


def log_api_call(endpoint: str, method: str = "GET", status_code: int = None, 
                 duration: float = None) -> None:
    """
    Log padronizado para chamadas de API.
    
    Args:
        endpoint: Endpoint da API
        method: Método HTTP
        status_code: Código de status da resposta
        duration: Duração da chamada em segundos
    """
    logger = get_logger("api_calls")
    
    message = f"API {method} {endpoint}"
    if status_code:
        message += f" | Status: {status_code}"
    if duration:
        message += f" | Duração: {duration:.2f}s"
    
    logger.info(message)


def log_error_with_context(error: Exception, context: dict = None) -> None:
    """
    Log padronizado para erros com contexto.
    
    Args:
        error: Exceção capturada
        context: Contexto adicional do erro
    """
    logger = get_logger("errors")
    
    message = f"Erro: {type(error).__name__}: {str(error)}"
    if context:
        message += f" | Contexto: {context}"
    
    logger.error(message, exc_info=True)


class LoggingContext:
    """Context manager para logging com contexto específico."""
    
    def __init__(self, operation: str, logger_name: str = None):
        """
        Inicializa o contexto de logging.
        
        Args:
            operation: Nome da operação
            logger_name: Nome do logger (opcional)
        """
        self.operation = operation
        self.logger = get_logger(logger_name or "operations")
        self.start_time = None
    
    def __enter__(self):
        """Inicia o contexto."""
        self.start_time = datetime.now()
        self.logger.info(f"Iniciando operação: {self.operation}")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Finaliza o contexto."""
        duration = (datetime.now() - self.start_time).total_seconds()
        
        if exc_type is None:
            self.logger.info(f"Operação concluída: {self.operation} ({duration:.2f}s)")
        else:
            self.logger.error(
                f"Operação falhou: {self.operation} ({duration:.2f}s) - "
                f"{exc_type.__name__}: {exc_val}"
            )
        
        return False  # Não suprimir exceções


# Configuração padrão para importação simples
def configure_default_logging():
    """Configura logging padrão para o projeto."""
    return setup_logging(
        log_level="INFO",
        log_to_file=True,
        log_file=None
    )


def setup_script_logging(script_name: str, log_level: str = "INFO", verbose: bool = False) -> logging.Logger:
    """
    Configura logging específico para scripts com nome personalizado.
    
    Args:
        script_name: Nome do script (ex: 'figma_extract', 'storybook_extract')
        log_level: Nível de log base
        verbose: Se True, usa DEBUG level
        
    Returns:
        Logger configurado para o script
    """
    # Determinar nível de log
    final_log_level = "DEBUG" if verbose else log_level
    
    # Configurar logging com nome personalizado
    setup_logging(
        log_level=final_log_level,
        log_to_file=True,
        script_name=script_name
    )
    
    # Retornar logger específico para o script
    return get_logger(f"src.scripts.{script_name}")
