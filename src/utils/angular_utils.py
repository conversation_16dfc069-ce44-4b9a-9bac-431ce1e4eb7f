# -*- coding: utf-8 -*-
"""
Utilitários específicos para Angular.

Este módulo contém funções auxiliares para geração de código Angular,
incluindo normalização de nomes de componentes, classes e arquivos.
"""

import re
from typing import Dict, Any, List, Tuple
from pathlib import Path

def normalize_to_pascal_case(name: str) -> str:
    """
    Converte nome para PascalCase para classes Angular.
    
    Args:
        name: Nome original
        
    Returns:
        Nome em PascalCase
    """
    # Remover caracteres especiais e normalizar
    clean_name = re.sub(r'[<>:"/\\|?*]', '', name)
    
    # Converter espaços e hífens para underscores temporariamente
    normalized = re.sub(r'[\s\-]+', '_', clean_name)
    
    # Converter para PascalCase
    words = normalized.split('_')
    pascal_case = ''.join(word.capitalize() for word in words if word)
    
    return pascal_case or 'Component'

def normalize_to_kebab_case(name: str) -> str:
    """
    Converte nome para kebab-case para nomes de arquivo Angular.
    
    Args:
        name: Nome original
        
    Returns:
        Nome em kebab-case
    """
    # Remover caracteres especiais e normalizar
    clean_name = re.sub(r'[<>:"/\\|?*]', '', name)
    
    # Converter espaços para hífens
    kebab_name = re.sub(r'\s+', '-', clean_name)
    
    # Remover hífens duplicados
    kebab_name = re.sub(r'-+', '-', kebab_name)
    
    # Remover hífens no início e fim
    kebab_name = kebab_name.strip('-')
    
    # Converter para minúsculo
    kebab_name = kebab_name.lower()
    
    return kebab_name or 'component'

def snake_to_kebab_case(snake_name: str) -> str:
    """
    Converte snake_case para kebab-case para arquivos Angular.
    
    Args:
        snake_name: Nome em snake_case (com underscores)
        
    Returns:
        Nome em kebab-case (com hífens)
    """
    # Converter underscores para hífens
    kebab_name = snake_name.replace('_', '-')
    
    # Remover hífens duplicados
    kebab_name = re.sub(r'-+', '-', kebab_name)
    
    # Remover hífens no início e fim
    kebab_name = kebab_name.strip('-')
    
    return kebab_name or 'component'

def normalize_component_name(name: str) -> str:
    """
    Normaliza nome para padrão Angular mantendo caracteres originais.
    
    Args:
        name: Nome original do componente
        
    Returns:
        Nome normalizado para arquivos Angular
    """
    # Manter o nome original, apenas normalizar espaços para hífens
    # e remover caracteres problemáticos para nomes de arquivo
    clean_name = re.sub(r'[<>:"/\\|?*]', '', name)
    
    # Converter espaços para hífens, mas manter underscores
    kebab_name = re.sub(r'\s+', '-', clean_name)
    
    # Remover hífens duplicados
    kebab_name = re.sub(r'-+', '-', kebab_name)
    
    # Remover hífens no início e fim
    kebab_name = kebab_name.strip('-')
    
    # Converter para minúsculo para consistência
    kebab_name = kebab_name.lower()
    
    return kebab_name or 'unnamed-component'

def generate_angular_component_files(component_name: str, html: str, ts: str, scss: str, output_dir: str) -> Path:
    """
    Salva arquivos do componente Angular em pasta específica.
    
    Args:
        component_name: Nome normalizado do componente (snake_case)
        html: Conteúdo HTML
        ts: Conteúdo TypeScript
        scss: Conteúdo SCSS
        output_dir: Diretório de saída
        
    Returns:
        Path do diretório do componente criado
    """
    from pathlib import Path
    
    # Converter snake_case para kebab-case para arquivos Angular
    kebab_name = snake_to_kebab_case(component_name)
    
    out_dir = Path(output_dir)
    
    # Criar pasta específica para o componente (kebab-case)
    component_dir = out_dir / kebab_name
    component_dir.mkdir(parents=True, exist_ok=True)
    
    # Salvar HTML
    with open(component_dir / f"{kebab_name}.component.html", 'w', encoding='utf-8') as f:
        f.write(html)
    
    # Salvar TS
    with open(component_dir / f"{kebab_name}.component.ts", 'w', encoding='utf-8') as f:
        f.write(ts)
    
    # Salvar SCSS
    with open(component_dir / f"{kebab_name}.component.scss", 'w', encoding='utf-8') as f:
        f.write(scss)
    
    return component_dir

 