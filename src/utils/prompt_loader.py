"""
Prompt Loader - Carrega prompts centralizados do arquivo YAML
"""

import yaml
from pathlib import Path
from typing import Dict, Any, Optional
from src.utils.logging import get_logger

logger = get_logger(__name__)


class PromptLoader:
    """
    Carrega e formata prompts centralizados do arquivo prompts.yaml
    """
    
    def __init__(self, prompts_file: str = "ai_prompts/prompts.yaml"):
        self.prompts_file = Path(prompts_file)
        self._prompts_cache: Optional[Dict[str, Any]] = None
        
    def _load_prompts(self) -> Dict[str, Any]:
        """Carrega prompts do arquivo YAML."""
        if self._prompts_cache is not None:
            return self._prompts_cache
            
        if not self.prompts_file.exists():
            logger.error(f"Arquivo de prompts não encontrado: {self.prompts_file}")
            return {}
        
        try:
            with open(self.prompts_file, 'r', encoding='utf-8') as f:
                self._prompts_cache = yaml.safe_load(f)
            logger.info("Prompts carregados com sucesso")
            return self._prompts_cache
        except Exception as e:
            logger.error(f"Erro ao carregar prompts: {e}")
            return {}
    
    def get_system_prompt(self, framework: str) -> str:
        """
        Constrói o prompt do sistema para o framework especificado.
        
        Args:
            framework: 'angular' ou 'react'
            
        Returns:
            Prompt do sistema completo
        """
        prompts = self._load_prompts()
        system_prompts = prompts.get("system_prompts", {})
        
        if framework not in system_prompts:
            logger.warning(f"Framework {framework} não encontrado nos prompts")
            return self._get_fallback_system_prompt(framework)
        
        framework_prompt = system_prompts[framework]
        
        # Construir prompt completo
        system_prompt_parts = [
            framework_prompt.get("role", ""),
            "",
            "## Sua Missão",
            framework_prompt.get("mission", ""),
            "",
            framework_prompt.get("process", ""),
            "",
            framework_prompt.get("guidelines", ""),
            "",
            framework_prompt.get("output_format", "")
        ]
        
        return "\n".join(system_prompt_parts)
    
    def get_user_prompt(self, template_name: str, **kwargs) -> str:
        """
        Formata um prompt do usuário com os parâmetros fornecidos.
        
        Args:
            template_name: Nome do template no arquivo YAML
            **kwargs: Parâmetros para formatação
            
        Returns:
            Prompt do usuário formatado
        """
        prompts = self._load_prompts()
        user_prompts = prompts.get("user_prompts", {})
        
        if template_name not in user_prompts:
            logger.warning(f"Template {template_name} não encontrado")
            return ""
        
        template = user_prompts[template_name]
        
        try:
            # Adicionar requisitos específicos do framework se disponível
            framework = kwargs.get("framework", "")
            if framework:
                framework_specific = prompts.get("framework_specific", {}).get(framework, {})
                kwargs["framework_specific_requirements"] = framework_specific.get("requirements", "")
            
            return template.format(**kwargs)
        except KeyError as e:
            logger.error(f"Parâmetro obrigatório ausente no template {template_name}: {e}")
            return template
        except Exception as e:
            logger.error(f"Erro ao formatar template {template_name}: {e}")
            return template
    
    def _get_fallback_system_prompt(self, framework: str) -> str:
        """Prompt do sistema de fallback se o carregamento falhar."""
        return f"""
Você é um desenvolvedor {framework} especialista em converter designs Figma em componentes {framework} de produção usando Design Systems.

## Sua Tarefa
Gerar componentes {framework} completos e prontos para produção baseados em:
1. Dados estruturados do Figma (hierarquia de elementos, estilos, propriedades)
2. Contexto do Design System (componentes reutilizáveis, padrões, guidelines)

## Diretrizes
- Use a hierarquia do Figma para estruturar o código
- Extraia propriedades dinâmicas dos dados do Figma
- Mapeie estilos do Figma para classes CSS do Design System
- SEMPRE priorize componentes do Design System quando disponíveis
- Gere código limpo, tipado e bem estruturado
- Use convenções modernas do {framework}
- Implemente acessibilidade (ARIA labels, semantic HTML)

## Output Format
{'Gere EXATAMENTE 3 arquivos (component.ts, component.html, component.scss)' if framework == 'angular' else 'Gere EXATAMENTE 2 arquivos (Component.tsx, Component.module.scss)'}

Responda APENAS com o código dos arquivos, sem explicações adicionais.
"""
    
    def list_available_prompts(self) -> Dict[str, Any]:
        """Lista todos os prompts disponíveis."""
        prompts = self._load_prompts()
        return {
            "system_prompts": list(prompts.get("system_prompts", {}).keys()),
            "user_prompts": list(prompts.get("user_prompts", {}).keys()),
            "framework_specific": list(prompts.get("framework_specific", {}).keys())
        }
