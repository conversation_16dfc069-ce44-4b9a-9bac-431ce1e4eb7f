# -*- coding: utf-8 -*-
"""
Component Generator - Geração final de código Angular.

Este módulo é responsável pela geração final de código Angular a partir dos dados
processados do Figma e mapeamentos com o Design System.
"""

import json
import re
from pathlib import Path
from typing import Dict, Any, List, Tuple
from dataclasses import dataclass
import tiktoken

# Carregar variáveis de ambiente
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    print("⚠️ python-dotenv não instalado. Tentando carregar .env manualmente...")
    import os
    env_path = Path(__file__).parent.parent.parent / '.env'
    if env_path.exists():
        with open(env_path, 'r') as f:
            for line in f:
                if line.strip() and not line.startswith('#'):
                    key, value = line.strip().split('=', 1)
                    os.environ[key] = value

from src.utils.config import ConfigLoader
from src.utils.logging import get_logger
from src.utils.file import sanitize_name
from src.utils.angular_utils import (
    normalize_to_pascal_case, 
    normalize_to_kebab_case, 
    normalize_component_name,
    generate_angular_component_files
)
from src.utils.figma_utils import (
    extract_css_from_figma,
    extract_component_sets,
    extract_components,
    extract_children_recursive,
    analyze_figma_structure,
    load_design_system_colors,
    extract_structure_info
)
from src.generators.utils.figma_data_processor import FigmaDataProcessor
from src.generators.figma_reader import FigmaComponentData
from src.generators.design_system_mapper import DesignSystemMapping
from src.utils.color_mapper import ColorMapper, create_color_mapper_from_design_system

logger = get_logger(__name__)

@dataclass
class GeneratedComponent:
    """Componente Angular gerado."""
    name: str
    html_template: str
    typescript_code: str
    scss_styles: str
    metadata: Dict[str, Any]

class ComponentGenerator:
    """
    Gera código final (HTML, TS, SCSS) usando IA, prompts centralizados e templates do Design System.
    """
    def __init__(self, config_path: str = "project_config.yaml", flow_client=None):
        self.config_loader = ConfigLoader(config_path)
        self.config = self.config_loader.load_config()
        self.ai_config = self.config_loader.get_ai_config()
        self.prompts = self._load_prompts()
        self.flow_client = flow_client or self._initialize_flow_api()
        
        # Inicializar processador de dados do Figma
        self.figma_processor = FigmaDataProcessor(flow_client=self.flow_client)

    def _load_prompts(self) -> Dict[str, Any]:
        import yaml
        prompts_path = Path(__file__).parent / "prompts.yaml"
        with open(prompts_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)

    def _initialize_flow_api(self):
        try:
            from flow_api import FlowAPIClient
            client = FlowAPIClient()
            connection = client.check_connection()
            if connection['status'] == 'connected':
                # Não logar aqui pois pode ser chamado quando Flow API já foi inicializado
                return client
            else:
                logger.warning(f"⚠️ Flow API não disponível - status: {connection['status']}")
                return None
        except Exception as e:
            logger.warning(f"⚠️ Erro ao inicializar Flow API: {e}")
            return None

    def generate_component_code(self, figma_data: FigmaComponentData, mapping: DesignSystemMapping, output_dir: str):
        """
        Gera HTML, TS e SCSS para o componente Angular completo.
        """
        logger.info(f"🚀 Gerando código final para: {figma_data.component_name}")
        
        # Verificar se é um componente wrapper
        is_wrapper = self._is_wrapper_component(figma_data)
        child_components = self._get_child_components(figma_data)
        
        if is_wrapper and child_components:
            logger.info(f"📦 Componente wrapper detectado. Componentes filhos: {child_components}")
            # Para wrappers, apenas salvar metadados para geração posterior
            self._save_wrapper_metadata(figma_data, child_components, output_dir)
            return
        
        logger.info(f"🔧 Componente individual detectado")
        # Pré-processar dados do Figma com IA
        ai_processed_data = self.figma_processor.preprocess_with_ai(figma_data)
        
        # Preparar contexto completo para IA
        context = self._prepare_complete_context(figma_data, mapping, ai_processed_data)
        
        # Gerar código Angular completo
        html = self._generate_complete_html(context)
        ts = self._generate_complete_typescript(context)
        scss = self._generate_complete_scss(context)
        
        # Salvar arquivos do componente Angular
        self._save_component_files(figma_data.normalized_name, html, ts, scss, output_dir)
        
        logger.info(f"✅ Componente Angular gerado: {figma_data.component_name}")

    def _save_wrapper_metadata(self, figma_data: FigmaComponentData, child_components: List[str], output_dir: str):
        """
        Salva metadados do wrapper para geração posterior via IA.
        
        Args:
            figma_data: Dados do Figma
            child_components: Lista de componentes filhos
            output_dir: Diretório de saída
        """
        import json
        
        # Normalizar nomes dos componentes filhos
        normalized_children = []
        for child in child_components:
            normalized_children.append(normalize_to_kebab_case(child))
        
        # Criar metadados do wrapper
        wrapper_metadata = {
            'wrapper_name': figma_data.component_name,
            'normalized_name': figma_data.normalized_name,
            'figma_id': figma_data.figma_id,
            'child_components': normalized_children,
            'figma_data': figma_data.__dict__,
            'output_dir': output_dir
        }
        
        # Salvar metadados em arquivo JSON na pasta figma_processed
        figma_processed_dir = output_dir.replace('/angular/', '/figma_processed/')
        metadata_path = Path(figma_processed_dir) / f"{figma_data.normalized_name}_wrapper_metadata.json"
        
        with open(metadata_path, 'w', encoding='utf-8') as f:
            json.dump(wrapper_metadata, f, indent=2, ensure_ascii=False)
        
        logger.info(f"💾 Metadados do wrapper salvos: {metadata_path}")
    

    def _prepare_complete_context(self, figma_data: FigmaComponentData, mapping: DesignSystemMapping, ai_processed_data: Dict[str, Any]) -> Dict[str, Any]:
        """Prepara contexto completo para geração do componente Angular."""
        
        logger.info(f"🔍 Preparando contexto para: {figma_data.component_name}")
        logger.debug(f"   Figma ID: {figma_data.figma_id}")
        logger.debug(f"   Dados processados pela IA: {len(ai_processed_data)} seções")
        
        # Mapear webcomponents para templates do Design System
        mapped_components = []
        loaded_templates = set()  # Para detectar duplicatas
        
        for i, map_item in enumerate(mapping.mappings):
            if map_item.design_system_component:
                template_file = map_item.design_system_component.file_path
                
                # Verificar se template já foi carregado
                if template_file in loaded_templates:
                    logger.debug(f"   Template duplicado ignorado: {template_file}")
                    continue  # Pular duplicatas
                
                template = self._load_ds_template(template_file)
                loaded_templates.add(template_file)
                logger.debug(f"   Template {len(loaded_templates)}: {template_file}")
                
                mapped_components.append({
                    "figma": map_item.figma_webcomponent,
                    "design_system": {
                        "name": map_item.design_system_component.name,
                        "description": map_item.design_system_component.description,
                        "template": template,
                        "category": map_item.design_system_component.category
                    },
                    "confidence": map_item.confidence,
                    "properties_mapping": map_item.properties_mapping
                })
        
        # Mapear cores do Figma para classes CSS do Design System
        logger.debug(f"   Mapeando cores do Figma...")
        color_mapping = self._map_figma_colors_to_css_classes(figma_data)
        logger.debug(f"   Cores mapeadas: {len(color_mapping)} classes")
        
        # Extrair informações de estrutura
        logger.debug(f"   Extraindo informações de estrutura...")
        structure_info = self._extract_structure_info(figma_data)
        
        # Contexto completo para IA (convertido para dict serializável)
        context = {
            "component_name": figma_data.component_name,
            "figma_id": figma_data.figma_id,
            "raw_html": figma_data.html_structure,
            "ai_processed_data": ai_processed_data,
            "mapped_components": mapped_components,
            "total_components": len(mapped_components),
            "component_type": self._detect_component_type(figma_data),
            "color_mapping": color_mapping,
            "structure_info": structure_info,
            "metadata": {
                "figma_file": figma_data.metadata.get('figma_file', ''),
                "component_type": figma_data.metadata.get('component_type', ''),
                "webcomponents_count": figma_data.metadata.get('webcomponents_count', 0),
                "has_children": figma_data.metadata.get('has_children', False),
                "generation_method": figma_data.metadata.get('generation_method', ''),
                "total_webcomponents": figma_data.metadata.get('total_webcomponents', 0)
            }
        }
        
        # Adicionar conteúdo específico do header se existir
        if structure_info.get('header_content'):
            logger.debug(f"   Adicionando conteúdo do header")
            context['header_content'] = structure_info['header_content']
        
        # Adicionar conteúdo específico das actions se existir
        if structure_info.get('actions_content'):
            logger.debug(f"   Adicionando conteúdo das actions")
            context['actions_content'] = structure_info['actions_content']
        
        # Log resumo do contexto
        logger.debug(f"Resumo do contexto:")
        logger.debug(f"   Templates carregados: {len(loaded_templates)}")
        logger.debug(f"   Classes de cores: {len(color_mapping)}")
        logger.debug(f"   Seções de estrutura: {len(structure_info)}")
        logger.debug(f"   HTML do Figma: {len(figma_data.html_structure)} chars")
        logger.debug(f"   Dados processados pela IA: {len(ai_processed_data)} seções")
        
        return context

    def _detect_component_type(self, figma_data: FigmaComponentData) -> str:
        """Detecta o tipo do componente baseado no nome e estrutura."""
        name = figma_data.component_name.lower()
        
        if 'modal' in name or 'dialog' in name:
            return 'modal'
        elif 'form' in name or 'input' in name:
            return 'form'
        elif 'button' in name:
            return 'button'
        elif 'card' in name:
            return 'card'
        elif 'table' in name:
            return 'table'
        else:
            return 'component'

    def _is_wrapper_component(self, figma_data: FigmaComponentData) -> bool:
        """Detecta se o componente é um wrapper (arquivo principal)."""
        # Verificar se há outros componentes no metadata
        all_components = figma_data.metadata.get('all_components_names', [])
        current_name = figma_data.component_name
        
        # Se há outros componentes além do atual, este é um wrapper
        other_components = [comp for comp in all_components if comp != current_name]
        
        # O wrapper é sempre o componente principal (primeiro na lista)
        is_main_component = all_components and current_name == all_components[0]
        
        return is_main_component and len(other_components) > 0

    def _get_child_components(self, figma_data: FigmaComponentData) -> List[str]:
        """Obtém a lista de componentes filhos."""
        all_components = figma_data.metadata.get('all_components_names', [])
        current_name = figma_data.component_name
        
        # Retornar todos os componentes exceto o atual
        return [comp for comp in all_components if comp != current_name]

    def _sanitize_ai_response(self, response: str) -> str:
        """Sanitiza a resposta da IA removendo blocos de código desnecessários."""
        if not response:
            return ""
        
        # Remover apenas os marcadores de início e fim de blocos de código
        # Preservar o conteúdo dentro dos blocos
        sanitized = response
        
        # Remover ```typescript, ```html, ```scss no início de linhas
        sanitized = re.sub(r'^```(?:typescript|html|scss|css|javascript|js|ts|angular|component)\s*$', '', sanitized, flags=re.MULTILINE)
        
        # Remover ``` no final de linhas
        sanitized = re.sub(r'^\s*```\s*$', '', sanitized, flags=re.MULTILINE)
        
        # Remover linhas vazias extras
        sanitized = re.sub(r'\n\s*\n\s*\n', '\n\n', sanitized)
        
        # Remover espaços em branco no início e fim
        sanitized = sanitized.strip()
        
        # Se a resposta estiver vazia após sanitização, retornar fallback
        if not sanitized:
            return "<!-- Conteúdo gerado pela IA -->"
        
        return sanitized

    def _generate_complete_html(self, context: Dict[str, Any]) -> str:
        """Gera HTML completo do componente Angular."""
        if not self.flow_client:
            logger.error("❌ Flow API não disponível para geração de HTML.")
            return self._generate_fallback_html(context)
        
        logger.info(f"Gerando HTML para: {context['component_name']}")
        logger.debug(f"   Componentes mapeados: {context['total_components']}")
        logger.debug(f"   Dados processados pela IA: {len(context.get('ai_processed_data', {}))}")
        
        # Construir system prompt
        system_prompt = self.prompts['system_prompts']['html_generation']['role'] + "\n" + \
                       self.prompts['system_prompts']['html_generation']['mission'] + "\n" + \
                       self.prompts['system_prompts']['html_generation']['guidelines']
        
        # Construir user prompt
        user_prompt = self.prompts['user_prompts']['html_generation']['task']
        user_prompt += "\n\nGere o HTML completo do componente Angular '" + context['component_name'] + "' usando os dados pré-processados pela IA."
        
        # Adicionar informações específicas sobre estrutura
        if context.get('structure_info', {}).get('has_header'):
            user_prompt += "\n\nIMPORTANTE: Este modal TEM um header com título. INCLUA o header no início do modal seguindo o template obrigatório."
            
            # Adicionar conteúdo específico do header se disponível
            if context.get('header_content'):
                header_content = context['header_content']
                user_prompt += f"\n\nCONTEÚDO DO HEADER:"
                user_prompt += f"\n- Nome: {header_content.get('name', '')}"
                user_prompt += f"\n- HTML: {header_content.get('html', '')}"
                user_prompt += f"\n- Props: {header_content.get('props', {})}"
        
        if context.get('structure_info', {}).get('layout_type') == 'modal_with_header':
            user_prompt += "\n\nESTRUTURA OBRIGATÓRIA: header -> conteúdo -> actions"
        
        # Log do contexto sendo enviado
        context_json = json.dumps(context, ensure_ascii=False, indent=2)
        # Count tokens using tiktoken library
        encoding = tiktoken.get_encoding("cl100k_base")
        token_count = len(encoding.encode(context_json))
        logger.debug(f"   Enviando contexto para IA ({token_count} tokens)")
        
        user_prompt += "\n\n" + context_json
        
        try:
            model = self.ai_config['model']['generation']
            temperature = self.ai_config.get('temperature', 0.1)
            output_tokens = self.ai_config.get('output_tokens', 16000)
            response = self.flow_client.with_model(model).get_answer(
                system_prompt=system_prompt,
                user_prompt=user_prompt,
                temperature=temperature,
                output_tokens=output_tokens
            )
            
            # Log detalhado da resposta
            logger.debug(f"   Resposta da IA recebida: {len(response)} caracteres")
            logger.debug(f"   Primeiros 200 chars: {response[:200]}...")
            logger.debug(f"   Últimos 200 chars: {response[-200:] if len(response) > 200 else response}")
            
            sanitized_response = self._sanitize_ai_response(response)
            logger.debug(f"   Resposta sanitizada: {len(sanitized_response)} caracteres")
            
            return sanitized_response
        except Exception as e:
            logger.error(f"❌ Erro na geração de HTML: {e}")
            return self._generate_fallback_html(context)

    def _generate_complete_typescript(self, context: Dict[str, Any]) -> str:
        """Gera TypeScript completo do componente Angular."""
        if not self.flow_client:
            logger.error("❌ Flow API não disponível para geração de TypeScript.")
            return self._generate_fallback_typescript(context)
        
        logger.info(f"Gerando TypeScript para: {context['component_name']}")
        logger.debug(f"   Componentes mapeados: {context['total_components']}")
        logger.debug(f"   Dados processados pela IA: {len(context.get('ai_processed_data', {}))}")
        
        # Construir system prompt
        system_prompt = self.prompts['system_prompts']['typescript_generation']['role'] + "\n" + \
                       self.prompts['system_prompts']['typescript_generation']['mission'] + "\n" + \
                       self.prompts['system_prompts']['typescript_generation']['guidelines']
        
        # Construir user prompt
        user_prompt = self.prompts['user_prompts']['typescript_generation']['task']
        user_prompt += "\n\nGere o TypeScript completo do componente Angular '" + context['component_name'] + "' usando os dados pré-processados pela IA."
        
        # Adicionar informações específicas sobre estrutura
        if context.get('structure_info', {}).get('has_header'):
            user_prompt += "\n\nIMPORTANTE: Este modal TEM um header com título. INCLUA o header no início do modal seguindo o template obrigatório."
            
            # Adicionar conteúdo específico do header se disponível
            if context.get('header_content'):
                header_content = context['header_content']
                user_prompt += f"\n\nCONTEÚDO DO HEADER:"
                user_prompt += f"\n- Nome: {header_content.get('name', '')}"
                user_prompt += f"\n- HTML: {header_content.get('html', '')}"
                user_prompt += f"\n- Props: {header_content.get('props', {})}"
        
        if context.get('structure_info', {}).get('layout_type') == 'modal_with_header':
            user_prompt += "\n\nESTRUTURA OBRIGATÓRIA: header -> conteúdo -> actions"
        
        # Log do contexto sendo enviado
        context_json = json.dumps(context, ensure_ascii=False, indent=2)
        # Count tokens using tiktoken library
        encoding = tiktoken.get_encoding("cl100k_base")
        token_count = len(encoding.encode(context_json))
        logger.debug(f"   Enviando contexto para IA ({token_count} tokens)")
        
        user_prompt += "\n\n" + context_json
        
        try:
            model = self.ai_config['model']['generation']
            temperature = self.ai_config.get('temperature', 0.1)
            output_tokens = self.ai_config.get('output_tokens', 16000)
            response = self.flow_client.with_model(model).get_answer(
                system_prompt=system_prompt,
                user_prompt=user_prompt,
                temperature=temperature,
                output_tokens=output_tokens
            )
            
            # Log detalhado da resposta
            logger.debug(f"   Resposta da IA recebida: {len(response)} caracteres")
            logger.debug(f"   Primeiros 200 chars: {response[:200]}...")
            logger.debug(f"   Últimos 200 chars: {response[-200:] if len(response) > 200 else response}")
            
            sanitized_response = self._sanitize_ai_response(response)
            logger.debug(f"   Resposta sanitizada: {len(sanitized_response)} caracteres")
            
            return sanitized_response
        except Exception as e:
            logger.error(f"❌ Erro na geração de TypeScript: {e}")
            return self._generate_fallback_typescript(context)

    def _generate_complete_scss(self, context: Dict[str, Any]) -> str:
        """Gera SCSS completo do componente Angular."""
        if not self.flow_client:
            logger.error("❌ Flow API não disponível para geração de SCSS.")
            return self._generate_fallback_scss(context)
        
        logger.info(f"Gerando SCSS para: {context['component_name']}")
        logger.debug(f"   Componentes mapeados: {context['total_components']}")
        logger.debug(f"   Dados processados pela IA: {len(context.get('ai_processed_data', {}))}")
        
        # Construir system prompt
        system_prompt = self.prompts['system_prompts']['scss_generation']['role'] + "\n" + \
                       self.prompts['system_prompts']['scss_generation']['mission'] + "\n" + \
                       self.prompts['system_prompts']['scss_generation']['guidelines']
        
        # Construir user prompt
        user_prompt = self.prompts['user_prompts']['scss_generation']['task']
        user_prompt += "\n\nGere o SCSS completo do componente Angular '" + context['component_name'] + "' usando os dados pré-processados pela IA."
        
        # Adicionar informações específicas sobre estrutura
        if context.get('structure_info', {}).get('has_header'):
            user_prompt += "\n\nIMPORTANTE: Este modal TEM um header com título. INCLUA o header no início do modal seguindo o template obrigatório."
            
            # Adicionar conteúdo específico do header se disponível
            if context.get('header_content'):
                header_content = context['header_content']
                user_prompt += f"\n\nCONTEÚDO DO HEADER:"
                user_prompt += f"\n- Nome: {header_content.get('name', '')}"
                user_prompt += f"\n- HTML: {header_content.get('html', '')}"
                user_prompt += f"\n- Props: {header_content.get('props', {})}"
        
        if context.get('structure_info', {}).get('layout_type') == 'modal_with_header':
            user_prompt += "\n\nESTRUTURA OBRIGATÓRIA: header -> conteúdo -> actions"
        
        # Log do contexto sendo enviado
        context_json = json.dumps(context, ensure_ascii=False, indent=2)
        # Count tokens using tiktoken library
        encoding = tiktoken.get_encoding("cl100k_base")
        token_count = len(encoding.encode(context_json))
        logger.debug(f"   Enviando contexto para IA ({token_count} tokens)")
        
        user_prompt += "\n\n" + context_json
        
        try:
            model = self.ai_config['model']['generation']
            temperature = self.ai_config.get('temperature', 0.1)
            output_tokens = self.ai_config.get('output_tokens', 16000)
            response = self.flow_client.with_model(model).get_answer(
                system_prompt=system_prompt,
                user_prompt=user_prompt,
                temperature=temperature,
                output_tokens=output_tokens
            )
            
            # Log detalhado da resposta
            logger.debug(f"   Resposta da IA recebida: {len(response)} caracteres")
            logger.debug(f"   Primeiros 200 chars: {response[:200]}...")
            logger.debug(f"   Últimos 200 chars: {response[-200:] if len(response) > 200 else response}")
            
            sanitized_response = self._sanitize_ai_response(response)
            logger.debug(f"   Resposta sanitizada: {len(sanitized_response)} caracteres")
            
            return sanitized_response
        except Exception as e:
            logger.error(f"❌ Erro na geração de SCSS: {e}")
            return self._generate_fallback_scss(context)

    def _generate_fallback_html(self, context: Dict[str, Any]) -> str:
        """Gera HTML de fallback quando IA não está disponível."""
        component_name = context['component_name']
        html = f"""<!-- Componente Angular: {component_name} -->
<div class="{component_name}-container">
  <!-- ERRO NA GERAÇÃO, fallback criado -->
  <!-- TODO: Implementar estrutura HTML baseada nos componentes mapeados -->
  <p>Componente {component_name} - Estrutura HTML a ser implementada</p>
</div>"""
        return html

    def _generate_fallback_typescript(self, context: Dict[str, Any]) -> str:
        """Gera TypeScript de fallback quando IA não está disponível."""
        component_name = context['component_name']
        normalized_name = component_name.replace(' ', '-').lower()
        class_name = ''.join(word.capitalize() for word in normalized_name.split('-')) + 'Component'
        
        ts = f"""import {{ Component, Input, Output, EventEmitter }} from '@angular/core';

@Component({{
  selector: 'app-{normalized_name}',
  templateUrl: './{normalized_name}.component.html',
  styleUrls: ['./{normalized_name}.component.scss']
}})
export class {class_name} {{
  // ERRO NA GERAÇÃO, fallback criado
  // TODO: Implementar propriedades e métodos baseados nos componentes mapeados
}}"""
        return ts

    def _generate_fallback_scss(self, context: Dict[str, Any]) -> str:
        """Gera SCSS de fallback quando IA não está disponível."""
        component_name = context['component_name']
        scss = f""".{component_name}-container {{
  // ERRO NA GERAÇÃO, fallback criado
  // TODO: Implementar estilos baseados nos componentes mapeados
}}"""
        return scss




    def _load_ds_template(self, file_path: str) -> str:
        try:
            if not file_path:
                logger.debug(f"   ⚠️ Caminho de template vazio")
                return ""
            
            # Verificar se arquivo existe
            if not Path(file_path).exists():
                logger.warning(f"   Arquivo não encontrado: {file_path}")
                return ""
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                logger.debug(f"   Carregado: {file_path} ({len(content)} chars)")
                return content
        except Exception as e:
            logger.warning(f"   Erro ao carregar template do Design System: {e}")
            return ""
    
    def _map_figma_colors_to_css_classes(self, figma_data: FigmaComponentData) -> Dict[str, str]:
        """
        Mapeia cores do Figma para classes CSS do Design System.
        
        Args:
            figma_data: Dados do Figma
            
        Returns:
            Dict com mapeamento de cores para classes CSS
        """
        try:
            # Carregar dados do Design System para cores
            design_system_data = self._load_design_system_colors()
            
            if not design_system_data:
                logger.warning("⚠️ Dados do Design System não encontrados para mapeamento de cores")
                return {}
            
            # Criar mapeador de cores
            color_mapper = create_color_mapper_from_design_system(design_system_data)
            
            # Converter dados do Figma para formato esperado
            figma_dict = {
                'id': figma_data.figma_id,
                'name': figma_data.component_name,
                'css': figma_data.css_styles,
                'children': []
            }
            
            # Adicionar children se disponíveis
            if figma_data.metadata.get('children'):
                figma_dict['children'] = figma_data.metadata.get('children', [])
            
            logger.debug(f"🎨 Dados do Figma para mapeamento de cores: {figma_dict}")
            
            # Mapear cores
            color_mapping = color_mapper.map_figma_colors_to_css_classes(figma_dict)
            
            logger.info(f"🎨 Mapeadas {len(color_mapping)} cores do Figma para classes CSS")
            return color_mapping
            
        except Exception as e:
            logger.error(f"❌ Erro no mapeamento de cores: {e}")
            return {}
    
    def _load_design_system_colors(self) -> Dict[str, Any]:
        """Carrega dados de cores do Design System."""
        return load_design_system_colors()
    
    def _extract_structure_info(self, figma_data: FigmaComponentData) -> Dict[str, Any]:
        """
        Extrai informações de estrutura do Figma analisando elementos HTML.
        
        Args:
            figma_data: Dados do Figma
            
        Returns:
            Dict com informações de estrutura
        """
        return extract_structure_info(figma_data.__dict__)

    def _save_component_files(self, component_name: str, html: str, ts: str, scss: str, output_dir: str):
        """Salva arquivos do componente Angular em pasta específica."""
        # Usar função do utilitário Angular
        component_dir = generate_angular_component_files(component_name, html, ts, scss, output_dir)
        logger.info(f"💾 Componente Angular salvo em {component_dir}") 