# Design to Code

Sistema avançado para converter designs do Figma em código final Angular/React usando Design Systems, com extração de componentes e filtro de visibilidade automático.

| Neste momento configurado para gerar Angular e trabalhar com o design system Liquid do Bradesco

## 🚀 Funcionalidades Principais

### Extração de Componentes do Figma

- **Acesso simplificado**: Necessário somente a geração de um token de acesso ao projeto do Figma por qualquer usuário com acesso Dev, uma única vez
- **Entrada Flexível**: Aceita Node ID ou URL do Figma
- **Exploração Interativa**: Navegação por nós filhos e extração de múltiplos componentes
- **JSONs Estruturados**: Gera arquivos JSON normalizados com hierarquia organizada a partir da resposta da API do Figma
- **Detecção de componentes do Design System**: Identifica componentes disponibilizados pelo Storybook
- **Estrutura Organizada**: Separa arquivo principal e componentes filhos em pastas
- **Filtro de Visibilidade**: Remove automaticamente elementos invisíveis do design

### Sistema de Design System

- **Extração de Storybook**: Carrega componentes do Storybook automaticamente
- **Parsers Especializados**: Processa diferentes tipos de componentes (templates, classes, images)
- **Formato Markdown**: Gera documentação em Markdown para uso com IA
- **Cache Inteligente**: Verifica versões e evita re-extração desnecessária
- **Configurado para design system Bradesco**: parsers identificam classes e definições específicas
- **Detecção de Versão**: Identifica automaticamente mudanças no Storybook

### Mapeamento com Design System

- **Busca Híbrida**: Combina BM25 para busca rápida e IA como fallback
- **Mapeamento de Propriedades**: Mapeia automaticamente propriedades do Figma para o Design System
- **Fallbacks Inteligentes**: Usa IA apenas quando necessário, com tratamento de erros detalhado
- **Alta Precisão**: Sistema de busca em múltiplas camadas com alta taxa de sucesso
- **Índice BM25 Automático**: Construído automaticamente para busca rápida

### Geração Final com IA

- Usa Flow API para gerar código Angular
- Geração de componentes individuais e wrappers
- Aplica templates do Design System
- Gera HTML, TypeScript e SCSS
- Mantém fidelidade aos dados do Figma
- Segue boas práticas do Angular (templateUrl, styleUrls, Outputs sem prefixo "on")
- **Convenções de nomeação**: Converte automaticamente snake_case para kebab-case nos arquivos Angular

## 📋 Pré-requisitos

- Python 3.10+
- FileKey (ou URL) e Token de acesso do Figma
- Configuração do Design System (Storybook)
- Dados de conexão com Flow via API

## 🛠️ Instalação

1. Clone o repositório:

```bash
git clone <repository-url>
cd design_to_code
```

2. Instale as dependências:

```bash
pip install -r requirements.txt
```

3. Preencha o arquivo de configuração:

```bash
cp project_config.example.yaml project_config.yaml
```

4. Edite `project_config.yaml` com os dados necessários, conforme os comentários

5. Preencha o arquivo de configurações de ambiente:

```bash
cp .env.example .env
```

6. Edite o arquivo `.env` com seus dados

## 🎯 Uso Simplificado

O projeto inclui um script `run.sh` que facilita a execução dos comandos principais de forma interativa:

### Configuração Inicial

```bash
./run.sh setup
```

Este comando:

- Cria o arquivo `project_config.yaml` a partir do exemplo
- Cria o arquivo `.env` a partir do exemplo
- Cria os diretórios necessários em `data/`

### Extração do Figma

```bash
./run.sh figma
```

### Extração do Design System

```bash
./run.sh storybook
```

### Geração de Código

```bash
./run.sh generate
```

#### Limpeza

```bash
./run.sh clean
# Remove todos os dados gerados (com confirmação).
```

## 🎯 Uso Avançado

### Extração Interativa do Figma

```bash
python -m src.scripts.figma_extract
```

### Extração do Design System

```bash
# Extrair componentes do Storybook
python -m src.scripts.storybook_extract
```

```bash
# Outras opções:

# Extrair apenas componentes de uma categoria específica
python -m src.scripts.storybook_extract --category components

# Extrair com limite para testes
python -m src.scripts.storybook_extract --limit 10

# Forçar atualização mesmo com mesma versão
python -m src.scripts.storybook_extract --force-update

# Salvar em formato JSON estruturado
python -m src.scripts.storybook_extract --save-json

# Extrai somente de uma categoria
python -m src.scripts.storybook_extract --category TYPE

# Debug de URLs encontradas
python -m src.scripts.storybook_extract --debug-urls
```

#### Categorias Disponíveis

- `components` - Componentes principais
- `templates` - Templates
- `services` - Serviços
- `classes` - Classes/utilities
- `animation` - Animações (Images)
- `flags` - Bandeiras (Images)
- `icons` - Ícones (Images)
- `illustration` - Ilustrações (Images)
- `logos` - Logos (Images)

### Geração de Código

```bash
# Geraçao interativa
python -m src.scripts.code_generator

# Gerar código a partir de uma extração expecífica
python -m src.scripts.code_generator --extraction data/figma_extraction/projeto/componente

# Geração apenas do HTML e estrutura básica
python -m src.scripts.code_generator --raw_only
```

## 📊 Estrutura de Saída

### Extração do Figma

```
data/figma_extraction/
├── nome_do_projeto/
│   ├── nome_do_node/
│   │   ├── main.json          # Arquivo principal
│   │   └── components/        # Componentes filhos
│   │       ├── child1.json
│   │       ├── child2.json
│   │       └── ...
```

### Design System

```
data/design_system/
├── meu_design_system/
│   ├── components/            # Componentes principais
│   │   ├── component1.md
│   │   └── ...
│   ├── templates/             # Templates e layouts
│   │   ├── template1.md
│   │   └── ...
│   ├── services/              # Serviços e utilitários
│   │   ├── service1.md
│   │   └── ...
│   ├── classes/               # Classes CSS utilitárias
│   │   ├── class1.md
│   │   └── ...
│   ├── images/                # Ícones, logos e imagens
│   │   ├── icons.md
│   │   ├── logos.md
│   │   └── ...
│   ├── main_documentation.md     # Documentação oficial extraída
│   ├── storybook_index.md        # Índice com Documentação
│   └── storybook_index.json   # Índice estruturado
```

### Componentes Gerados

```
data/output_generated/
├── nome_do_projeto/
│   ├── modal-cadastro-novo-contador-generico/  # ✅ Kebab-case
│   │   ├── figma_processed/   # Dados processados do Figma
│   │   │   ├── raw.html       # HTML fiel do Figma
│   │   │   └── mapping.json   # Mapeamento com Design System
│   │   └── angular/           # Código Angular final
│   │       ├── modal-cadastro-novo-contador-generico.component.html  # ✅ Kebab-case
│   │       ├── modal-cadastro-novo-contador-generico.component.ts
│   │       └── modal-cadastro-novo-contador-generico.component.scss
```

### Fluxo de Funcionamento

#### 1. Extração do Figma

```
Figma API → figma_extract.py → figma_normalized_extractor.py → JSONs estruturados
```

#### 2. Extração do Design System

```
Storybook → storybook_scraper.py → storybook_extractor.py → Markdown/JSON
```

**Fase 1 - Scraping (storybook_scraper.py):**

- Navega para o Storybook
- Expande menus automaticamente
- Extrai URLs dos componentes
- Filtra componentes não desejados (release-log, support, HTML vs Webcomponent)
- Prioriza webcomponentes por categoria

**Fase 2 - Extração (storybook_extractor.py):**

- Processa URLs extraídas
- Usa parsers especializados por tipo de componente
- Gera Markdown ou JSON estruturado
- Salva em diferentes formatos conforme flags

#### 3. Geração de Código

```
JSONs + Markdown → code_generator.py → Código Angular/React
```

**Fluxo de 3 Etapas:**

1. **Leitura do Figma**: Extrai dados estruturados e webcomponents
2. **Mapeamento Design System**: BM25 + IA para mapeamento inteligente
3. **Geração com IA**: Código Angular final com boas práticas

## 🐛 Troubleshooting

### Logs Detalhados

O sistema gera logs detalhados em `data/logs/` para facilitar o debug.

---

**Desenvolvido com ❤️ e 🤖 para automatizar a conversão de designs em código**
